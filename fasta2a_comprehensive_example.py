#!/usr/bin/env python3
"""
FastA2A Comprehensive Example Script

This script demonstrates all the features of the FastA2A library for implementing
the Agent2Agent (A2A) protocol in Python. It includes:

1. Basic Pydantic AI Agent to A2A Server conversion
2. Custom Storage, Broker, and Worker implementations
3. Context management and conversation continuity
4. Task lifecycle management
5. Complete A2A server setup with all components

The script can be run as separate components or as one complete example.

Dependencies:
    pip install pydantic-ai-slim[a2a] uvicorn starlette pydantic opentelemetry-api

Usage:
    # Run the basic Pydantic AI to A2A server:
    python fasta2a_example.py basic

    # Run the complete custom implementation:
    python fasta2a_example.py complete

    # Run as ASGI server:
    uvicorn fasta2a_example:app --host 0.0.0.0 --port 8000
"""

import asyncio
import uuid
import sys
from collections.abc import AsyncIterator
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json

# FastA2A and related imports
from fasta2a import FastA2A, Worker
from fasta2a.broker import InMemoryBroker, Broker
from fasta2a.schema import (
    Artifact, Message, TaskIdParams, TaskSendParams, TextPart, DataPart,
    Task, TaskState, AgentCard, Skill
)
from fasta2a.storage import InMemoryStorage, Storage

# Pydantic AI imports
try:
    from pydantic_ai import Agent, RunContext
    from pydantic import BaseModel, Field
    PYDANTIC_AI_AVAILABLE = True
except ImportError:
    print("Warning: pydantic-ai not available. Basic example will not work.")
    PYDANTIC_AI_AVAILABLE = False


# ============================================================================
# COMPONENT 1: Basic Pydantic AI Agent to A2A Server
# ============================================================================

def create_basic_pydantic_ai_server():
    """
    Creates a basic A2A server using Pydantic AI's built-in to_a2a() method.
    
    This is the simplest way to expose a Pydantic AI agent as an A2A server.
    The agent automatically handles conversation context, task management,
    and artifact creation.
    
    Returns:
        FastA2A: ASGI application that can be run with uvicorn
    """
    if not PYDANTIC_AI_AVAILABLE:
        raise ImportError("pydantic-ai is required for basic example")
    
    # Define a simple Pydantic model for structured responses
    class ChatResponse(BaseModel):
        response: str = Field(description="The agent's response")
        confidence: float = Field(description="Confidence level 0-1", ge=0, le=1)
        context_length: int = Field(description="Number of messages in context")
    
    # Create a Pydantic AI agent with structured output
    agent = Agent(
        'openai:gpt-4',  # You can change this to any supported model
        instructions="""
        You are a helpful AI assistant that provides informative and engaging responses.
        Always be polite and try to be helpful. Analyze the conversation context
        and provide appropriate responses.
        """,
        result_type=ChatResponse  # This will create DataPart artifacts
    )
    
    # Convert to A2A server - this handles all the complexity automatically
    app = agent.to_a2a()
    
    print("✅ Basic Pydantic AI to A2A server created")
    print("   - Automatic context management")
    print("   - Built-in task lifecycle handling")
    print("   - Structured data artifacts")
    
    return app


# ============================================================================
# COMPONENT 2: Custom Storage Implementation
# ============================================================================

class EnhancedStorage(Storage[List[Message]]):
    """
    Enhanced storage implementation that extends InMemoryStorage with additional features.
    
    This storage provides:
    - Task persistence with metadata
    - Context storage with conversation history
    - Task search and filtering capabilities
    - Statistics and analytics
    
    In a production environment, this would typically use a database like PostgreSQL,
    Redis, or MongoDB for persistence.
    """
    
    def __init__(self):
        """Initialize the enhanced storage with in-memory data structures."""
        super().__init__()
        self._tasks: Dict[str, Task] = {}
        self._contexts: Dict[str, List[Message]] = {}
        self._task_metadata: Dict[str, Dict[str, Any]] = {}
        
    async def submit_task(self, context_id: str, message: Message) -> Task:
        """
        Submit a new task to storage.
        
        Creates a new task with a unique ID and associates it with the given context.
        Stores metadata about task creation time and initial message.
        
        Args:
            context_id: The conversation context identifier
            message: The initial message that triggered this task
            
        Returns:
            Task: The created task object
        """
        task_id = str(uuid.uuid4())
        
        task = Task(
            id=task_id,
            context_id=context_id,
            state='pending',
            history=[message],
            artifacts=[],
            created_at=datetime.now().isoformat()
        )
        
        self._tasks[task_id] = task
        self._task_metadata[task_id] = {
            'created_at': datetime.now(),
            'initial_message_length': len(message.parts[0].text if message.parts else 0),
            'message_count': 1
        }
        
        print(f"📝 Task {task_id[:8]}... submitted for context {context_id[:8]}...")
        return task
        
    async def load_task(self, task_id: str, history_length: Optional[int] = None) -> Optional[Task]:
        """
        Load a task from storage by ID.
        
        Args:
            task_id: The unique task identifier
            history_length: Optional limit on message history length
            
        Returns:
            Task or None: The task if found, None otherwise
        """
        task = self._tasks.get(task_id)
        if task and history_length is not None:
            # Limit history length if requested
            limited_task = task.copy()
            limited_task['history'] = task['history'][-history_length:]
            return limited_task
        return task
        
    async def update_task(
        self,
        task_id: str,
        state: TaskState,
        new_artifacts: Optional[List[Artifact]] = None,
        new_messages: Optional[List[Message]] = None,
    ) -> Task:
        """
        Update task state and optionally add new artifacts and messages.
        
        Args:
            task_id: The task identifier
            state: New task state ('pending', 'working', 'completed', 'failed')
            new_artifacts: Optional list of artifacts to add
            new_messages: Optional list of messages to add to history
            
        Returns:
            Task: The updated task
        """
        task = self._tasks[task_id]
        task['state'] = state
        
        if new_artifacts:
            task['artifacts'].extend(new_artifacts)
            
        if new_messages:
            task['history'].extend(new_messages)
            self._task_metadata[task_id]['message_count'] += len(new_messages)
            
        self._task_metadata[task_id]['last_updated'] = datetime.now()
        
        print(f"🔄 Task {task_id[:8]}... updated to state: {state}")
        return task
        
    async def load_context(self, context_id: str) -> Optional[List[Message]]:
        """
        Load conversation context (message history) for a given context ID.
        
        Args:
            context_id: The conversation context identifier
            
        Returns:
            List[Message] or None: The conversation history if found
        """
        return self._contexts.get(context_id)
        
    async def update_context(self, context_id: str, context: List[Message]) -> None:
        """
        Update the conversation context with new messages.
        
        Args:
            context_id: The conversation context identifier
            context: The complete updated conversation history
        """
        self._contexts[context_id] = context
        print(f"💬 Context {context_id[:8]}... updated with {len(context)} messages")
        
    def get_task_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about tasks in storage.
        
        Returns:
            Dict containing task statistics
        """
        total_tasks = len(self._tasks)
        states = {}
        for task in self._tasks.values():
            state = task['state']
            states[state] = states.get(state, 0) + 1
            
        return {
            'total_tasks': total_tasks,
            'task_states': states,
            'total_contexts': len(self._contexts),
            'average_messages_per_task': sum(
                meta['message_count'] for meta in self._task_metadata.values()
            ) / max(total_tasks, 1)
        }


# ============================================================================
# COMPONENT 3: Custom Broker Implementation
# ============================================================================

class EnhancedBroker(Broker):
    """
    Enhanced broker implementation with task queuing and worker management.
    
    This broker provides:
    - Task queue management with priorities
    - Worker registration and health monitoring
    - Task distribution and load balancing
    - Retry logic for failed tasks
    
    In production, this would typically use Redis, RabbitMQ, or Apache Kafka
    for distributed task queuing.
    """
    
    def __init__(self):
        """Initialize the enhanced broker."""
        super().__init__()
        self._task_queue: List[Dict[str, Any]] = []
        self._workers: Dict[str, Dict[str, Any]] = {}
        self._worker_task_count: Dict[str, int] = {}
        
    async def submit_task(self, params: TaskSendParams) -> None:
        """
        Submit a task to the broker queue.
        
        Args:
            params: Task parameters including task ID and metadata
        """
        task_info = {
            'params': params,
            'submitted_at': datetime.now(),
            'priority': params.get('priority', 5),  # Default priority
            'retry_count': 0
        }
        
        # Insert task in priority order (lower number = higher priority)
        inserted = False
        for i, queued_task in enumerate(self._task_queue):
            if task_info['priority'] < queued_task['priority']:
                self._task_queue.insert(i, task_info)
                inserted = True
                break
                
        if not inserted:
            self._task_queue.append(task_info)
            
        print(f"📋 Task {params['id'][:8]}... queued (priority: {task_info['priority']})")
        
    async def get_task(self, worker_id: str) -> Optional[TaskSendParams]:
        """
        Get the next task for a worker.
        
        Args:
            worker_id: Unique identifier for the requesting worker
            
        Returns:
            TaskSendParams or None: Next task parameters if available
        """
        if not self._task_queue:
            return None
            
        # Get the highest priority task
        task_info = self._task_queue.pop(0)
        params = task_info['params']
        
        # Track worker assignment
        self._worker_task_count[worker_id] = self._worker_task_count.get(worker_id, 0) + 1
        
        print(f"🎯 Task {params['id'][:8]}... assigned to worker {worker_id[:8]}...")
        return params
        
    async def register_worker(self, worker_id: str, capabilities: Optional[Dict[str, Any]] = None) -> None:
        """
        Register a worker with the broker.
        
        Args:
            worker_id: Unique identifier for the worker
            capabilities: Optional worker capabilities metadata
        """
        self._workers[worker_id] = {
            'registered_at': datetime.now(),
            'capabilities': capabilities or {},
            'status': 'active'
        }
        self._worker_task_count[worker_id] = 0
        
        print(f"🤖 Worker {worker_id[:8]}... registered")
        
    async def unregister_worker(self, worker_id: str) -> None:
        """
        Unregister a worker from the broker.
        
        Args:
            worker_id: Worker identifier to unregister
        """
        if worker_id in self._workers:
            del self._workers[worker_id]
            
        if worker_id in self._worker_task_count:
            del self._worker_task_count[worker_id]
            
        print(f"❌ Worker {worker_id[:8]}... unregistered")
        
    def get_broker_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the broker state.
        
        Returns:
            Dict containing broker statistics
        """
        return {
            'queued_tasks': len(self._task_queue),
            'registered_workers': len(self._workers),
            'worker_task_counts': self._worker_task_count.copy(),
            'queue_priorities': [task['priority'] for task in self._task_queue]
        }


# ============================================================================
# COMPONENT 4: Custom Worker Implementation
# ============================================================================

class AdvancedWorker(Worker[List[Message]]):
    """
    Advanced worker implementation that processes tasks with sophisticated logic.
    
    This worker provides:
    - Natural language processing and response generation
    - Context-aware conversation handling
    - Multiple response formats (text and structured data)
    - Error handling and retry logic
    - Performance monitoring
    
    In a real implementation, this would integrate with your chosen AI framework
    (OpenAI, Anthropic, local models, etc.).
    """
    
    def __init__(self, storage: Storage, broker: Broker, worker_id: Optional[str] = None):
        """
        Initialize the advanced worker.
        
        Args:
            storage: Storage implementation for data persistence
            broker: Broker implementation for task management
            worker_id: Optional custom worker identifier
        """
        self.worker_id = worker_id or str(uuid.uuid4())
        super().__init__(storage=storage, broker=broker)
        self.processed_tasks = 0
        self.start_time = datetime.now()
        
    async def run_task(self, params: TaskSendParams) -> None:
        """
        Execute a task with comprehensive processing logic.
        
        This method:
        1. Loads the task and context
        2. Processes the conversation history
        3. Generates appropriate responses
        4. Creates artifacts based on response type
        5. Updates storage with results
        
        Args:
            params: Task parameters including task ID
        """
        task_id = params['id']
        print(f"🚀 Worker {self.worker_id[:8]}... starting task {task_id[:8]}...")
        
        try:
            # Load task from storage
            task = await self.storage.load_task(task_id)
            if not task:
                print(f"❌ Task {task_id[:8]}... not found in storage")
                return
                
            # Update task state to working
            await self.storage.update_task(task_id, state='working')
            
            # Load conversation context
            context = await self.storage.load_context(task['context_id']) or []
            
            # Add current task history to context
            full_context = context + task.get('history', [])
            
            # Process the conversation and generate response
            response_data = await self._process_conversation(full_context, task['context_id'])
            
            # Create response message
            response_message = Message(
                role='agent',
                parts=response_data['parts'],
                kind='message',
                message_id=str(uuid.uuid4()),
            )
            
            # Update context with new message
            full_context.append(response_message)
            await self.storage.update_context(task['context_id'], full_context)
            
            # Create artifacts from response
            artifacts = self._create_artifacts(response_data['result'])
            
            # Update task with completion
            await self.storage.update_task(
                task_id,
                state='completed',
                new_messages=[response_message],
                new_artifacts=artifacts
            )
            
            self.processed_tasks += 1
            print(f"✅ Worker {self.worker_id[:8]}... completed task {task_id[:8]}...")
            
        except Exception as e:
            print(f"💥 Worker {self.worker_id[:8]}... failed task {task_id[:8]}...: {e}")
            await self.storage.update_task(task_id, state='failed')
            
    async def cancel_task(self, params: TaskIdParams) -> None:
        """
        Cancel a running task.
        
        Args:
            params: Task cancellation parameters
        """
        task_id = params['id']
        print(f"🛑 Worker {self.worker_id[:8]}... cancelling task {task_id[:8]}...")
        
        # In a real implementation, this would interrupt ongoing processing
        await self.storage.update_task(task_id, state='failed')
        
    async def _process_conversation(self, context: List[Message], context_id: str) -> Dict[str, Any]:
        """
        Process conversation context and generate appropriate response.
        
        This is where you would integrate with your AI model/framework.
        For demonstration, we'll create rule-based responses.
        
        Args:
            context: Full conversation history
            context_id: Conversation context identifier
            
        Returns:
            Dict containing response parts and structured result
        """
        # Get the last user message
        user_messages = [msg for msg in context if msg['role'] == 'user']
        last_message = user_messages[-1] if user_messages else None
        
        if not last_message:
            response_text = "Hello! How can I help you today?"
            confidence = 1.0
        else:
            # Extract text from last message
            message_text = ""
            for part in last_message.get('parts', []):
                if part.get('kind') == 'text':
                    message_text += part.get('text', '')
                    
            # Simple response generation based on keywords
            response_text, confidence = self._generate_response(message_text, len(context))
            
        # Create text part
        text_part = TextPart(
            text=response_text,
            kind='text'
        )
        
        # Create structured data (this would come from your AI model)
        structured_result = {
            'response': response_text,
            'confidence': confidence,
            'context_length': len(context),
            'worker_id': self.worker_id,
            'processed_at': datetime.now().isoformat()
        }
        
        return {
            'parts': [text_part],
            'result': structured_result
        }
        
    def _generate_response(self, message_text: str, context_length: int) -> tuple[str, float]:
        """
        Generate a response based on the input message.
        
        This is a simple rule-based system for demonstration.
        In practice, this would use your AI model.
        
        Args:
            message_text: The user's message text
            context_length: Number of messages in conversation
            
        Returns:
            Tuple of (response_text, confidence_score)
        """
        message_lower = message_text.lower()
        
        # Define response patterns
        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return f"Hello! This is our conversation message #{context_length + 1}. How can I assist you?", 0.9
            
        elif any(word in message_lower for word in ['help', 'assist', 'support']):
            return f"I'm here to help! I've been tracking our conversation which now has {context_length} messages. What do you need assistance with?", 0.8
            
        elif any(word in message_lower for word in ['weather', 'temperature']):
            return "I don't have access to real-time weather data, but I can help you think about weather-related questions or suggest resources!", 0.7
            
        elif any(word in message_lower for word in ['time', 'date']):
            return f"I processed your message at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}. Our conversation has {context_length} messages so far.", 0.9
            
        elif any(word in message_lower for word in ['calculate', 'math', 'compute']):
            return "I can help with calculations! Please provide the specific math problem you'd like me to solve.", 0.8
            
        else:
            return f"I understand you said: '{message_text}'. This is message #{context_length + 1} in our conversation. Could you tell me more about what you need?", 0.6
            
    def _create_artifacts(self, result_data: Any) -> List[Artifact]:
        """
        Create artifacts from the response data.
        
        Args:
            result_data: The structured result data to convert to artifacts
            
        Returns:
            List of artifacts
        """
        artifacts = []
        
        # Create a data artifact with the structured response
        data_artifact = Artifact(
            artifact_id=str(uuid.uuid4()),
            kind='data',
            data=DataPart(
                data=json.dumps(result_data),
                media_type='application/json',
                kind='data'
            ),
            metadata={
                'created_by': self.worker_id,
                'created_at': datetime.now().isoformat(),
                'data_type': 'structured_response'
            }
        )
        
        artifacts.append(data_artifact)
        
        return artifacts
        
    def build_message_history(self, history: List[Message]) -> List[Any]:
        """
        Convert message history to a format suitable for the AI model.
        
        Args:
            history: List of messages in A2A format
            
        Returns:
            List of messages in model-specific format
        """
        # This would convert A2A messages to your model's expected format
        # For example, OpenAI format, Anthropic format, etc.
        converted_messages = []
        
        for message in history:
            # Extract text content
            text_content = ""
            for part in message.get('parts', []):
                if part.get('kind') == 'text':
                    text_content += part.get('text', '')
                    
            converted_messages.append({
                'role': message['role'],
                'content': text_content
            })
            
        return converted_messages
        
    def build_artifacts(self, result: Any) -> List[Artifact]:
        """
        Build artifacts from AI model results.
        
        Args:
            result: Raw result from AI model
            
        Returns:
            List of artifacts
        """
        return self._create_artifacts(result)
        
    def get_worker_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about this worker's performance.
        
        Returns:
            Dict containing worker statistics
        """
        uptime = datetime.now() - self.start_time
        
        return {
            'worker_id': self.worker_id,
            'processed_tasks': self.processed_tasks,
            'uptime_seconds': uptime.total_seconds(),
            'tasks_per_minute': self.processed_tasks / max(uptime.total_seconds() / 60, 1),
            'start_time': self.start_time.isoformat()
        }


# ============================================================================
# COMPONENT 5: Complete A2A Server Setup
# ============================================================================

class ComprehensiveA2AServer:
    """
    Comprehensive A2A server that demonstrates all FastA2A features.
    
    This class orchestrates all components:
    - Enhanced storage for task and context management
    - Enhanced broker for task queuing and worker management
    - Advanced worker for intelligent task processing
    - Complete FastA2A server with lifecycle management
    """
    
    def __init__(self):
        """Initialize all components of the A2A server."""
        self.storage = EnhancedStorage()
        self.broker = EnhancedBroker()
        self.workers: List[AdvancedWorker] = []
        self.app: Optional[FastA2A] = None
        
    def create_agent_card(self) -> AgentCard:
        """
        Create an agent card describing this A2A server's capabilities.
        
        Returns:
            AgentCard: Description of the agent's skills and capabilities
        """
        # Define skills that this agent can perform
        skills = [
            Skill(
                skill_id="conversation",
                name="Natural Conversation",
                description="Engage in natural language conversations with context awareness",
                tags=["chat", "conversation", "general"],
                examples=[
                    "Have a friendly chat",
                    "Answer general questions",
                    "Provide information and assistance"
                ],
                input_media_types=["text/plain"],
                output_media_types=["text/plain", "application/json"]
            ),
            Skill(
                skill_id="math_help",
                name="Mathematical Assistance",
                description="Help with basic mathematical calculations and explanations",
                tags=["math", "calculation", "education"],
                examples=[
                    "Calculate 15% of 250",
                    "Explain basic algebra concepts",
                    "Help with word problems"
                ],
                input_media_types=["text/plain"],
                output_media_types=["text/plain", "application/json"]
            ),
            Skill(
                skill_id="context_tracking",
                name="Context Tracking",
                description="Maintain conversation context across multiple interactions",
                tags=["memory", "context", "conversation"],
                examples=[
                    "Remember previous topics discussed",
                    "Build upon earlier conversation points",
                    "Maintain conversation continuity"
                ],
                input_media_types=["text/plain"],
                output_media_types=["text/plain", "application/json"]
            )
        ]
        
        return AgentCard(
            name="FastA2A Demonstration Agent",
            description="""
            A comprehensive demonstration agent showcasing all FastA2A features.
            This agent can engage in natural conversations, help with basic math,
            and maintain context across multiple interactions. It demonstrates
            custom storage, broker, and worker implementations.
            """,
            url="http://localhost:8000",  # Would be your actual server URL
            version="1.0.0",
            skills=skills
        )
        
    async def setup_workers(self, num_workers: int = 2) -> None:
        """
        Set up multiple workers for distributed task processing.
        
        Args:
            num_workers: Number of workers to create
        """
        for i in range(num_workers):
            worker = AdvancedWorker(
                storage=self.storage,
                broker=self.broker,
                worker_id=f"worker-{i+1}-{str(uuid.uuid4())[:8]}"
            )
            
            # Register worker with broker
            await self.broker.register_worker(
                worker.worker_id,
                capabilities={
                    'max_context_length': 1000,
                    'supported_languages': ['en'],
                    'processing_types': ['text', 'structured_data']
                }
            )
            
            self.workers.append(worker)
            
        print(f"🤖 Set up {num_workers} workers")
        
    @asynccontextmanager
    async def lifespan(self, app: FastA2A) -> AsyncIterator[None]:
        """
        Manage the lifecycle of the A2A server.
        
        This function:
        1. Starts the task manager
        2. Starts all workers
        3. Yields control to the server
        4. Cleans up when shutting down
        
        Args:
            app: The FastA2A application instance
        """
        print("🚀 Starting A2A server lifecycle...")
        
        # Start the task manager
        async with app.task_manager:
            print("📋 Task manager started")
            
            # Start all workers
            worker_tasks = []
            for worker in self.workers:
                worker_task = asyncio.create_task(worker.run())
                worker_tasks.append(worker_task)
                
            print(f"👥 Started {len(self.workers)} workers")
            
            try:
                # Yield control to the server
                yield
                
            finally:
                # Cleanup: cancel all worker tasks
                print("🛑 Shutting down workers...")
                for task in worker_tasks:
                    task.cancel()
                    
                # Unregister workers from broker
                for worker in self.workers:
                    await self.broker.unregister_worker(worker.worker_id)
                    
                print("✅ A2A server shutdown complete")
                
    def create_app(self) -> FastA2A:
        """
        Create the complete FastA2A application.
        
        Returns:
            FastA2A: Configured ASGI application
        """
        # Create agent card
        agent_card = self.create_agent_card()
        
        # Create the FastA2A app with all components
        self.app = FastA2A(
            storage=self.storage,
            broker=self.broker,
            lifespan=self.lifespan,
            agent_card=agent_card
        )
        
        print("🌟 Complete FastA2A application created")
        print("   - Enhanced storage with statistics")
        print("   - Enhanced broker with prioritization")
        print("   - Advanced workers with AI simulation")
        print("   - Agent card with skill definitions")
        
        return self.app
        
    async def print_statistics(self) -> None:
        """Print comprehensive statistics about the server state."""
        print("\n" + "="*60)
        print("📊 A2A SERVER STATISTICS")
        print("="*60)
        
        # Storage statistics
        storage_stats = self.storage.get_task_statistics()
        print(f"📝 Storage: {storage_stats['total_tasks']} tasks, {storage_stats['total_contexts']} contexts")
        print(f"   States: {storage_stats['task_states']}")
        print(f"   Avg messages/task: {storage_stats['average_messages_per_task']:.1f}")
        
        # Broker statistics
        broker_stats = self.broker.get_broker_statistics()
        print(f"📋 Broker: {broker_stats['queued_tasks']} queued, {broker_stats['registered_workers']} workers")
        print(f"   Worker loads: {broker_stats['worker_task_counts']}")
        
        # Worker statistics
        print("🤖 Workers:")
        for worker in self.workers:
            worker_stats = worker.get_worker_statistics()
            print(f"   {worker_stats['worker_id'][:16]}...: {worker_stats['processed_tasks']} tasks, "
                  f"{worker_stats['tasks_per_minute']:.1f}/min")
        
        print("="*60 + "\n")


# ============================================================================
# COMPONENT 6: Testing and Demonstration Functions
# ============================================================================

async def test_basic_functionality():
    """
    Test basic A2A functionality with simulated client interactions.
    
    This function demonstrates:
    - Task creation and processing
    - Context management across multiple messages
    - Artifact generation
    - Statistics reporting
    """
    print("🧪 Testing basic A2A functionality...")
    
    # Create server components
    server = ComprehensiveA2AServer()
    await server.setup_workers(num_workers=2)
    
    # Simulate client interactions
    context_id = str(uuid.uuid4())
    
    # First interaction - greeting
    greeting_message = Message(
        role='user',
        parts=[TextPart(text='Hello! Can you help me with some questions?', kind='text')],
        kind='message',
        message_id=str(uuid.uuid4()),
    )
    
    task1 = await server.storage.submit_task(context_id, greeting_message)
    await server.broker.submit_task({'id': task1['id'], 'priority': 1})
    
    # Process first task
    if server.workers:
        worker = server.workers[0]
        task_params = await server.broker.get_task(worker.worker_id)
        if task_params:
            await worker.run_task(task_params)
    
    # Second interaction - math question
    math_message = Message(
        role='user',
        parts=[TextPart(text='What is 15% of 250?', kind='text')],
        kind='message',
        message_id=str(uuid.uuid4()),
    )
    
    task2 = await server.storage.submit_task(context_id, math_message)
    await server.broker.submit_task({'id': task2['id'], 'priority': 2})
    
    # Process second task
    if len(server.workers) > 1:
        worker = server.workers[1]
        task_params = await server.broker.get_task(worker.worker_id)
        if task_params:
            await worker.run_task(task_params)
    
    # Third interaction - context check
    context_message = Message(
        role='user',
        parts=[TextPart(text='Can you tell me what we have discussed so far?', kind='text')],
        kind='message',
        message_id=str(uuid.uuid4()),
    )
    
    task3 = await server.storage.submit_task(context_id, context_message)
    await server.broker.submit_task({'id': task3['id'], 'priority': 1})
    
    # Process third task
    if server.workers:
        worker = server.workers[0]
        task_params = await server.broker.get_task(worker.worker_id)
        if task_params:
            await worker.run_task(task_params)
    
    # Print results
    await server.print_statistics()
    
    # Show final context
    final_context = await server.storage.load_context(context_id)
    if final_context:
        print("💬 Final conversation context:")
        for i, msg in enumerate(final_context):
            role = msg['role']
            text = ""
            for part in msg.get('parts', []):
                if part.get('kind') == 'text':
                    text += part.get('text', '')
            print(f"   {i+1}. {role}: {text[:100]}...")
    
    print("✅ Basic functionality test completed\n")


async def test_multiple_contexts():
    """
    Test handling multiple conversation contexts simultaneously.
    
    This demonstrates the A2A server's ability to maintain separate
    conversation threads and process them concurrently.
    """
    print("🧪 Testing multiple conversation contexts...")
    
    server = ComprehensiveA2AServer()
    await server.setup_workers(num_workers=3)
    
    # Create three different conversation contexts
    contexts = [
        {
            'id': str(uuid.uuid4()),
            'theme': 'math_help',
            'messages': [
                'I need help with algebra',
                'Can you explain quadratic equations?',
                'What is the quadratic formula?'
            ]
        },
        {
            'id': str(uuid.uuid4()),
            'theme': 'general_chat',
            'messages': [
                'Hi there!',
                'How are you doing today?',
                'Tell me something interesting'
            ]
        },
        {
            'id': str(uuid.uuid4()),
            'theme': 'weather_questions',
            'messages': [
                'What\'s the weather like?',
                'Do you have weather forecasts?',
                'How can I check the weather?'
            ]
        }
    ]
    
    # Submit tasks for all contexts
    tasks = []
    for context in contexts:
        for i, message_text in enumerate(context['messages']):
            message = Message(
                role='user',
                parts=[TextPart(text=message_text, kind='text')],
                kind='message',
                message_id=str(uuid.uuid4()),
            )
            
            task = await server.storage.submit_task(context['id'], message)
            await server.broker.submit_task({
                'id': task['id'], 
                'priority': i + 1,  # Earlier messages have higher priority
                'context_theme': context['theme']
            })
            tasks.append(task)
    
    # Process all tasks using available workers
    processed_count = 0
    while processed_count < len(tasks):
        for worker in server.workers:
            task_params = await server.broker.get_task(worker.worker_id)
            if task_params:
                await worker.run_task(task_params)
                processed_count += 1
                if processed_count >= len(tasks):
                    break
    
    # Show results for each context
    for context in contexts:
        print(f"\n🗨️  Context: {context['theme']}")
        context_messages = await server.storage.load_context(context['id'])
        if context_messages:
            for i, msg in enumerate(context_messages):
                role = msg['role']
                text = ""
                for part in msg.get('parts', []):
                    if part.get('kind') == 'text':
                        text += part.get('text', '')
                print(f"   {i+1}. {role}: {text[:80]}...")
    
    await server.print_statistics()
    print("✅ Multiple contexts test completed\n")


def create_client_example():
    """
    Create example client code for interacting with the A2A server.
    
    This shows how external clients would communicate with the A2A server
    using HTTP requests following the A2A protocol.
    """
    client_code = '''
# Example A2A Client Implementation
import asyncio
import httpx
import json
from typing import Optional

class A2AClient:
    """Simple A2A protocol client for testing."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.context_id: Optional[str] = None
    
    async def send_message(self, message: str, context_id: Optional[str] = None) -> dict:
        """Send a message to the A2A server."""
        
        payload = {
            "message": {
                "role": "user",
                "parts": [{"kind": "text", "text": message}],
                "kind": "message"
            }
        }
        
        if context_id:
            payload["context_id"] = context_id
            
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/v1/messages",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            result = response.json()
            
            # Store context_id for future messages
            if "context_id" in result:
                self.context_id = result["context_id"]
                
            return result
    
    async def get_task_status(self, task_id: str) -> dict:
        """Check the status of a task."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/v1/tasks/{task_id}")
            return response.json()
    
    async def conversation_example(self):
        """Example conversation with the A2A server."""
        
        # Send first message
        result1 = await self.send_message("Hello! Can you help me?")
        print(f"Response 1: {result1}")
        
        # Continue conversation with context
        result2 = await self.send_message(
            "What is 25% of 400?", 
            context_id=self.context_id
        )
        print(f"Response 2: {result2}")
        
        # Check final task status
        if "task_id" in result2:
            status = await self.get_task_status(result2["task_id"])
            print(f"Final task status: {status}")

# Usage:
# client = A2AClient()
# await client.conversation_example()
'''
    
    print("📋 Example A2A Client Code:")
    print(client_code)


# ============================================================================
# MAIN EXECUTION AND COMMAND LINE INTERFACE
# ============================================================================

async def main():
    """
    Main function that demonstrates all FastA2A features.
    
    This can be run in different modes:
    - basic: Shows simple Pydantic AI to A2A conversion
    - complete: Shows full custom implementation
    - test: Runs comprehensive tests
    - client: Shows client example code
    """
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        mode = "help"
    
    if mode == "basic":
        print("🚀 Creating basic Pydantic AI to A2A server...")
        if PYDANTIC_AI_AVAILABLE:
            app = create_basic_pydantic_ai_server()
            print("✅ Basic server created. Run with: uvicorn fasta2a_example:app")
        else:
            print("❌ pydantic-ai not available for basic example")
            
    elif mode == "complete":
        print("🚀 Creating complete custom A2A implementation...")
        server = ComprehensiveA2AServer()
        await server.setup_workers(num_workers=2)
        app = server.create_app()
        print("✅ Complete server created. Run with: uvicorn fasta2a_example:app")
        
        # Store app globally for uvicorn
        globals()['app'] = app
        
    elif mode == "test":
        print("🧪 Running comprehensive A2A tests...\n")
        await test_basic_functionality()
        await test_multiple_contexts()
        print("✅ All tests completed!")
        
    elif mode == "client":
        print("📋 A2A Client Example:\n")
        create_client_example()
        
    else:
        print("""
🌟 FastA2A Comprehensive Example Script

This script demonstrates all features of the FastA2A library for implementing
the Agent2Agent (A2A) protocol in Python.

Usage:
    python fasta2a_example.py [mode]

Modes:
    basic     - Create basic Pydantic AI to A2A server
    complete  - Create complete custom A2A implementation  
    test      - Run comprehensive functionality tests
    client    - Show example client code
    help      - Show this help message

Examples:
    python fasta2a_example.py basic
    python fasta2a_example.py test
    uvicorn fasta2a_example:app --host 0.0.0.0 --port 8000

Features Demonstrated:
✅ Basic Pydantic AI agent to A2A server conversion
✅ Custom Storage implementation with task management
✅ Custom Broker implementation with task queuing
✅ Advanced Worker implementation with AI simulation
✅ Context management across multiple conversations
✅ Task lifecycle management and monitoring
✅ Artifact creation and management
✅ Agent card and skill definitions
✅ Statistics and performance monitoring
✅ Multi-worker distributed processing
✅ Priority-based task scheduling
✅ Error handling and retry logic

Requirements:
    pip install pydantic-ai-slim[a2a] uvicorn starlette pydantic opentelemetry-api
        """)

# Create a default app for uvicorn
# This will be overwritten if running in complete mode
if PYDANTIC_AI_AVAILABLE:
    try:
        app = create_basic_pydantic_ai_server()
    except Exception:
        # Fallback to complete implementation
        async def create_fallback_app():
            server = ComprehensiveA2AServer()
            await server.setup_workers(num_workers=1)
            return server.create_app()
        
        # Note: This is a simplified fallback - in practice you'd handle this differently
        print("Creating fallback complete implementation...")

if __name__ == "__main__":
    asyncio.run(main())